#!/usr/bin/env python3
"""
PDF Report Generator for M1 Survey Analysis
Creates a comprehensive PDF report with all findings and recommendations
"""

import json
from datetime import datetime

def create_html_report():
    """Create HTML report that can be converted to PDF"""
    
    # Load analysis results
    try:
        with open('comprehensive_analysis_results.json', 'r') as f:
            results = json.load(f)
    except FileNotFoundError:
        print("Please run comprehensive_report.py first to generate analysis results")
        return
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M1 Device Purchase Behavior Survey Analysis Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #fff;
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #007AFF;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #007AFF;
            margin: 0;
            font-size: 28px;
        }}
        .header p {{
            color: #666;
            margin: 5px 0;
            font-size: 16px;
        }}
        .section {{
            margin: 30px 0;
            page-break-inside: avoid;
        }}
        .section h2 {{
            color: #007AFF;
            border-bottom: 2px solid #007AFF;
            padding-bottom: 10px;
            font-size: 22px;
        }}
        .section h3 {{
            color: #333;
            margin-top: 25px;
            font-size: 18px;
        }}
        .overview-box {{
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }}
        .result-box {{
            background: #fff;
            border: 1px solid #007AFF;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .result-title {{
            color: #007AFF;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
        }}
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }}
        .data-table th, .data-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        .data-table th {{
            background-color: #007AFF;
            color: white;
            font-weight: bold;
        }}
        .data-table tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .insight-box {{
            background: #e8f5e8;
            border-left: 4px solid #4CAF50;
            padding: 15px;
            margin: 15px 0;
        }}
        .recommendation-box {{
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }}
        .key-stat {{
            font-size: 24px;
            font-weight: bold;
            color: #007AFF;
        }}
        .percentage {{
            font-weight: bold;
            color: #28a745;
        }}
        ul, ol {{
            padding-left: 20px;
        }}
        li {{
            margin: 8px 0;
        }}
        .footer {{
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>M1 Device Purchase Behavior Survey</h1>
        <h2>Comprehensive Analysis Report</h2>
        <p>Anniversary Campaign Period: April - Mid June</p>
        <p>Generated on: {datetime.now().strftime('%B %d, %Y')}</p>
    </div>

    <div class="section">
        <h2>Executive Summary</h2>
        <div class="overview-box">
            <h3>Survey Overview</h3>
            <p><strong>Total Responses:</strong> <span class="key-stat">{results['survey_overview']['total_responses']:,}</span></p>
            <p><strong>Survey Period:</strong> {results['survey_overview']['period']}</p>
            <p><strong>Target Audience:</strong> Postpaid customers who purchased devices during the anniversary campaign</p>
            <p><strong>Objective:</strong> Understand device purchase behavior and evaluate the effectiveness of the M1 Anniversary campaign</p>
        </div>
    </div>

    <div class="section">
        <h2>Result 1: Main Motivation for Phone Upgrade</h2>
        <div class="result-box">
            <div class="result-title">Customer Motivation Analysis</div>
            <p>Analysis of free-text responses categorized into key motivation drivers:</p>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Motivation Category</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    # Add motivation data
    total_motivation = sum(results['motivation_distribution'].values())
    for category, count in sorted(results['motivation_distribution'].items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_motivation) * 100
        html_content += f"""
                    <tr>
                        <td>{category}</td>
                        <td>{count:,}</td>
                        <td class="percentage">{percentage:.1f}%</td>
                    </tr>
        """
    
    html_content += f"""
                </tbody>
            </table>
            
            <div class="insight-box">
                <strong>Key Insight:</strong> Promotions and deals are the primary motivator ({(results['motivation_distribution']['Promotion'] / total_motivation * 100):.1f}%), 
                followed by phone issues ({(results['motivation_distribution']['Phone issue'] / total_motivation * 100):.1f}%) and general upgrade desires 
                ({(results['motivation_distribution']['Upgrade of phone'] / total_motivation * 100):.1f}%).
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Result 2: M1 Anniversary Campaign Awareness</h2>
        <div class="result-box">
            <div class="result-title">Campaign Reach Analysis</div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Campaign Awareness</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    # Add campaign awareness data
    total_campaign = sum(results['campaign_awareness'].values())
    for response, count in sorted(results['campaign_awareness'].items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_campaign) * 100
        html_content += f"""
                    <tr>
                        <td>{response}</td>
                        <td>{count:,}</td>
                        <td class="percentage">{percentage:.1f}%</td>
                    </tr>
        """
    
    aware_pct = (results['campaign_awareness']['Yes'] / total_campaign * 100)
    html_content += f"""
                </tbody>
            </table>
            
            <div class="insight-box">
                <strong>Key Insight:</strong> {aware_pct:.1f}% of customers were aware of the M1 Anniversary Campaign, 
                indicating good reach but room for improvement in campaign visibility.
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Result 3: Device Type Preferences</h2>
        <div class="result-box">
            <div class="result-title">Device Selection Analysis</div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Device Type</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    # Add device preference data
    total_devices = sum(results['device_preferences'].values())
    for device, count in sorted(results['device_preferences'].items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_devices) * 100
        html_content += f"""
                    <tr>
                        <td>{device}</td>
                        <td>{count:,}</td>
                        <td class="percentage">{percentage:.1f}%</td>
                    </tr>
        """
    
    iphone_pct = (results['device_preferences']['IPhone'] / total_devices * 100)
    html_content += f"""
                </tbody>
            </table>
            
            <div class="insight-box">
                <strong>Key Insight:</strong> Strong preference for iPhone ({iphone_pct:.1f}%) over Android devices, 
                indicating the importance of maintaining strong Apple partnerships and inventory.
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Result 4: Age Demographics</h2>
        <div class="result-box">
            <div class="result-title">Customer Age Distribution</div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Age Group</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    # Add age demographics data
    total_ages = sum(results['age_demographics'].values())
    # Sort age groups properly
    age_order = ['18-<20', '21-<25', '25-<30', '30-<35', '35-<40', '40-<45', '45-<50', '50-<55', '55-<60', '60-<65', '65-<70', '>=70']
    
    for age_group in age_order:
        # Handle HTML encoding in age groups
        for key in results['age_demographics'].keys():
            if age_group.replace('<', '&lt;').replace('>', '&gt;') == key or age_group == key:
                count = results['age_demographics'][key]
                percentage = (count / total_ages) * 100
                html_content += f"""
                    <tr>
                        <td>{key}</td>
                        <td>{count:,}</td>
                        <td class="percentage">{percentage:.1f}%</td>
                    </tr>
                """
                break
    
    html_content += f"""
                </tbody>
            </table>
            
            <div class="insight-box">
                <strong>Key Insight:</strong> The majority of customers are in the mature age segments (45-65 years), 
                representing a significant portion of the customer base that values reliability and established brands.
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Strategic Recommendations</h2>
        <div class="recommendation-box">
            <h3>Immediate Actions</h3>
            <ol>
    """
    
    for rec in results['recommendations']:
        html_content += f"<li>{rec}</li>"
    
    html_content += f"""
            </ol>
        </div>
        
        <div class="insight-box">
            <h3>Additional Strategic Insights</h3>
            <ul>
    """
    
    for insight in results['insights']:
        html_content += f"<li>{insight}</li>"
    
    html_content += f"""
            </ul>
        </div>
    </div>

    <div class="footer">
        <p>M1 Limited - Device Purchase Behavior Analysis</p>
        <p>Report generated on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}</p>
    </div>
</body>
</html>
    """
    
    # Save HTML report
    with open('M1_Survey_Analysis_Report.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("HTML report created: M1_Survey_Analysis_Report.html")
    print("You can open this file in a web browser and print to PDF")

if __name__ == "__main__":
    create_html_report()
