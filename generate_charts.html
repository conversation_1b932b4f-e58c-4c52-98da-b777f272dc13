<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M1 Survey Analysis Charts</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chart-wrapper {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .summary {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .insight {
            background: #f0f8e8;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }
    </style>
</head>
<body>
    <h1>M1 Device Purchase Behavior Survey Analysis</h1>
    <div class="summary">
        <h3>Survey Overview</h3>
        <p><strong>Period:</strong> Anniversary Campaign (April - Mid June)</p>
        <p><strong>Total Responses:</strong> 1,659</p>
        <p><strong>Objective:</strong> Understand device purchase behavior and campaign effectiveness</p>
    </div>

    <!-- Result 1: Motivation Distribution -->
    <div class="chart-container">
        <h2>Result 1: Main Motivation for Phone Upgrade</h2>
        <div class="chart-wrapper">
            <canvas id="motivationChart"></canvas>
        </div>
        <div class="insight">
            <strong>Key Insight:</strong> Promotions (27.4%) are the top driver, followed by general reasons (27.2%) and phone issues (18.1%)
        </div>
    </div>

    <!-- Result 2: Campaign Awareness -->
    <div class="chart-container">
        <h2>Result 2: M1 Anniversary Campaign Awareness</h2>
        <div class="chart-wrapper">
            <canvas id="campaignChart"></canvas>
        </div>
        <div class="insight">
            <strong>Key Insight:</strong> 58.9% were aware of the campaign, but 41.1% were not - opportunity for better reach
        </div>
    </div>

    <!-- Result 3: Device Preferences -->
    <div class="chart-container">
        <h2>Result 3: Device Type Preferences</h2>
        <div class="chart-wrapper">
            <canvas id="deviceChart"></canvas>
        </div>
        <div class="insight">
            <strong>Key Insight:</strong> Strong iPhone preference (61.7%) vs Android (36.5%) - focus iPhone inventory
        </div>
    </div>

    <!-- Result 4: Age Demographics -->
    <div class="chart-container">
        <h2>Result 4: Age Demographics</h2>
        <div class="chart-wrapper">
            <canvas id="ageChart"></canvas>
        </div>
        <div class="insight">
            <strong>Key Insight:</strong> Mature customers (45-65) represent 57.3% - tailor messaging for this segment
        </div>
    </div>

    <div class="summary">
        <h3>Key Recommendations</h3>
        <ol>
            <li><strong>Promotion Strategy:</strong> Continue strong promotional campaigns as they're the top motivator</li>
            <li><strong>Trade-in Programs:</strong> Expand trade-in offers for customer acquisition</li>
            <li><strong>iPhone Focus:</strong> Prioritize iPhone inventory and promotions</li>
            <li><strong>Mature Market:</strong> Target 45-65 age group with tailored messaging</li>
            <li><strong>Campaign Awareness:</strong> Improve campaign visibility and reach</li>
            <li><strong>Retention Strategy:</strong> Address phone issues proactively</li>
        </ol>
    </div>

    <script>
        // Chart.js configuration
        Chart.defaults.font.size = 12;
        Chart.defaults.color = '#333';

        // Result 1: Motivation Chart
        const motivationCtx = document.getElementById('motivationChart').getContext('2d');
        new Chart(motivationCtx, {
            type: 'bar',
            data: {
                labels: ['Promotion', 'Others', 'Phone issue', 'Upgrade of phone', 'Contract ending', 'Switch Provider'],
                datasets: [{
                    label: 'Number of Customers',
                    data: [454, 451, 301, 239, 213, 1],
                    backgroundColor: [
                        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'
                    ],
                    borderColor: '#333',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Customer Motivation Distribution (n=1,659)'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Customers'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Motivation Category'
                        }
                    }
                }
            }
        });

        // Result 2: Campaign Awareness Chart
        const campaignCtx = document.getElementById('campaignChart').getContext('2d');
        new Chart(campaignCtx, {
            type: 'doughnut',
            data: {
                labels: ['Yes (Aware)', 'No (Not Aware)'],
                datasets: [{
                    data: [977, 682],
                    backgroundColor: ['#4CAF50', '#FF5722'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'M1 Anniversary Campaign Awareness (n=1,659)'
                    }
                }
            }
        });

        // Result 3: Device Preferences Chart
        const deviceCtx = document.getElementById('deviceChart').getContext('2d');
        new Chart(deviceCtx, {
            type: 'pie',
            data: {
                labels: ['iPhone', 'Android Phone', 'Android Tablet', 'iPad', 'Others'],
                datasets: [{
                    data: [1023, 605, 13, 12, 6],
                    backgroundColor: [
                        '#007AFF', '#34C759', '#FF9500', '#5856D6', '#8E8E93'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Device Type Preferences (n=1,659)'
                    }
                }
            }
        });

        // Result 4: Age Demographics Chart
        const ageCtx = document.getElementById('ageChart').getContext('2d');
        new Chart(ageCtx, {
            type: 'bar',
            data: {
                labels: ['18-20', '21-25', '25-30', '30-35', '35-40', '40-45', '45-50', '50-55', '55-60', '60-65', '65-70', '70+'],
                datasets: [{
                    label: 'Number of Customers',
                    data: [1, 25, 57, 122, 151, 198, 293, 317, 192, 148, 75, 80],
                    backgroundColor: '#36A2EB',
                    borderColor: '#333',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Age Demographics Distribution (n=1,659)'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Customers'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Age Group'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
