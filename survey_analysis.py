import csv
import re
from collections import Counter
import json

def load_and_clean_data():
    """Load and clean the survey data"""
    data = []
    headers = []

    with open('data-exploration-export_Device_wave2.csv', 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        headers = next(csv_reader)  # Get headers
        for row in csv_reader:
            data.append(row)

    print(f"Dataset shape: {len(data)} rows, {len(headers)} columns")
    print(f"Total columns: {len(headers)}")

    # Find motivation column (should be column M, index 12)
    motivation_col_index = None
    for i, header in enumerate(headers):
        if 'Motivation to Purchase' in header:
            motivation_col_index = i
            break

    print(f"Motivation column found at index: {motivation_col_index}")
    return data, headers, motivation_col_index

def categorize_motivation(motivation_text):
    """Categorize customer motivation based on text analysis"""
    if pd.isna(motivation_text) or motivation_text == '':
        return 'Others'
    
    motivation_text = str(motivation_text).lower()
    
    # Define keywords for each category
    promotion_keywords = ['promotion', 'promo', 'discount', 'deal', 'offer', 'anniversary', 'trade in', 'trade-in', 
                         'rebate', 'free', 'attractive', 'good deal', 'great deal', 'birthday', 'campaign']
    
    upgrade_keywords = ['upgrade', 'new phone', 'latest', 'better', 'newer', 'technology', 'features', 
                       'camera', 'storage', 'performance', 'swift', 'switch brand', 'model']
    
    phone_issue_keywords = ['spoil', 'broken', 'damage', 'malfunction', 'battery', 'slow', 'lag', 'hang', 
                           'old phone', 'dying', 'issue', 'problem', 'not working', 'outdated']
    
    contract_keywords = ['contract', 'recontract', 're-contract', 'renewal', 'renew', 'end', 'ending', 
                        'expire', 'due', 'overdue']
    
    provider_keywords = ['switch provider', 'change provider', 'from singtel', 'from starhub', 
                        'cheaper than', 'better plan']
    
    # Check categories in order of priority
    if any(keyword in motivation_text for keyword in promotion_keywords):
        return 'Promotion'
    elif any(keyword in motivation_text for keyword in contract_keywords):
        return 'Contract ending'
    elif any(keyword in motivation_text for keyword in phone_issue_keywords):
        return 'Phone issue'
    elif any(keyword in motivation_text for keyword in upgrade_keywords):
        return 'Upgrade of phone'
    elif any(keyword in motivation_text for keyword in provider_keywords):
        return 'Switch Provider'
    else:
        return 'Others'

def analyze_motivations(data, motivation_col_index):
    """Analyze and categorize customer motivations"""
    if motivation_col_index is None:
        print("Motivation column not found!")
        return None, None

    # Extract motivations and categorize them
    motivations = []
    categories = []

    for row in data:
        if len(row) > motivation_col_index:
            motivation_text = row[motivation_col_index]
            motivations.append(motivation_text)
            category = categorize_motivation(motivation_text)
            categories.append(category)

    # Count categories
    motivation_counts = Counter(categories)
    total_responses = len(categories)

    print("\nMotivation Categories:")
    for category, count in motivation_counts.most_common():
        percentage = (count / total_responses) * 100
        print(f"{category}: {count} ({percentage:.1f}%)")

    return motivation_counts, motivations

def create_result1_chart(motivation_counts):
    """Create Result 1: Motivation Distribution Chart - Text-based visualization"""
    print("\n" + "="*60)
    print("RESULT 1: MOTIVATION DISTRIBUTION CHART")
    print("="*60)

    total_responses = sum(motivation_counts.values())

    # Create text-based bar chart
    max_count = max(motivation_counts.values())
    bar_width = 50  # Maximum width of bars in characters

    print(f"\nMain Motivation for Phone Upgrade (Total Responses: {total_responses})")
    print("-" * 60)

    for category, count in motivation_counts.most_common():
        percentage = (count / total_responses) * 100
        bar_length = int((count / max_count) * bar_width)
        bar = "█" * bar_length

        print(f"{category:<20} {bar:<50} {count:>4} ({percentage:>5.1f}%)")

    # Save results to JSON for potential chart creation later
    results = {
        'motivation_distribution': {
            category: {'count': count, 'percentage': round((count / total_responses) * 100, 1)}
            for category, count in motivation_counts.items()
        },
        'total_responses': total_responses
    }

    with open('result_1_data.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\nData saved to 'result_1_data.json'")
    return results

def analyze_anniversary_campaign_awareness(data, headers):
    """Analyze awareness of M1 Anniversary Campaign"""
    campaign_col = 'Were you aware of the M1 Anniversary Campaign?'

    # Find campaign awareness column
    campaign_col_index = None
    for i, header in enumerate(headers):
        if campaign_col in header:
            campaign_col_index = i
            break

    if campaign_col_index is None:
        print("Campaign awareness column not found!")
        return None

    # Extract campaign awareness data
    awareness_responses = []
    for row in data:
        if len(row) > campaign_col_index:
            response = row[campaign_col_index].strip()
            if response:
                awareness_responses.append(response)

    awareness_counts = Counter(awareness_responses)
    total_responses = len(awareness_responses)

    print(f"\nM1 Anniversary Campaign Awareness:")
    for awareness, count in awareness_counts.most_common():
        percentage = (count / total_responses) * 100
        print(f"{awareness}: {count} ({percentage:.1f}%)")

    return awareness_counts

def analyze_device_types(data, headers):
    """Analyze device types purchased"""
    device_col = 'Device Type'

    # Find device type column
    device_col_index = None
    for i, header in enumerate(headers):
        if device_col in header:
            device_col_index = i
            break

    if device_col_index is None:
        print("Device type column not found!")
        return None

    # Extract device type data
    device_responses = []
    for row in data:
        if len(row) > device_col_index:
            device = row[device_col_index].strip()
            if device:
                device_responses.append(device)

    device_counts = Counter(device_responses)
    total_responses = len(device_responses)

    print(f"\nDevice Types Purchased:")
    for device, count in device_counts.most_common():
        percentage = (count / total_responses) * 100
        print(f"{device}: {count} ({percentage:.1f}%)")

    return device_counts

def analyze_age_demographics(data, headers):
    """Analyze age demographics"""
    age_col = 'Age Band'

    # Find age band column
    age_col_index = None
    for i, header in enumerate(headers):
        if age_col in header:
            age_col_index = i
            break

    if age_col_index is None:
        print("Age band column not found!")
        return None

    # Extract age data
    age_responses = []
    for row in data:
        if len(row) > age_col_index:
            age = row[age_col_index].strip()
            if age:
                age_responses.append(age)

    age_counts = Counter(age_responses)
    total_responses = len(age_responses)

    print(f"\nAge Demographics:")
    for age, count in age_counts.most_common():
        percentage = (count / total_responses) * 100
        print(f"{age}: {count} ({percentage:.1f}%)")

    return age_counts

def main():
    """Main analysis function"""
    print("=== M1 Device Purchase Behavior Survey Analysis ===\n")

    # Load data
    data, headers, motivation_col_index = load_and_clean_data()

    # Analyze motivations (Result 1)
    print("\n" + "="*50)
    print("RESULT 1: MOTIVATION ANALYSIS")
    print("="*50)
    motivation_counts, motivations = analyze_motivations(data, motivation_col_index)

    if motivation_counts is not None:
        result1_data = create_result1_chart(motivation_counts)

    # Additional analyses for other results
    print("\n" + "="*50)
    print("ADDITIONAL ANALYSES")
    print("="*50)

    # Campaign awareness
    awareness_counts = analyze_anniversary_campaign_awareness(data, headers)

    # Device types
    device_counts = analyze_device_types(data, headers)

    # Age demographics
    age_counts = analyze_age_demographics(data, headers)

    return data, headers, motivation_counts, awareness_counts, device_counts, age_counts

if __name__ == "__main__":
    data, headers, motivation_counts, awareness_counts, device_counts, age_counts = main()
