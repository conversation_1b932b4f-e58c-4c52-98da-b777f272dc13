#!/usr/bin/env python3
"""
Comprehensive Survey Analysis Report
M1 Device Purchase Behavior Survey - Anniversary Campaign Period (Apr-mid June)
"""

import csv
import re
from collections import Counter
import json

def categorize_motivation(motivation_text):
    """Categorize customer motivation based on text analysis"""
    if not motivation_text or motivation_text.strip() == '':
        return 'Others'
    
    motivation_text = str(motivation_text).lower()
    
    # Define keywords for each category
    promotion_keywords = ['promotion', 'promo', 'discount', 'deal', 'offer', 'anniversary', 'trade in', 'trade-in', 
                         'rebate', 'free', 'attractive', 'good deal', 'great deal', 'birthday', 'campaign']
    
    upgrade_keywords = ['upgrade', 'new phone', 'latest', 'better', 'newer', 'technology', 'features', 
                       'camera', 'storage', 'performance', 'swift', 'switch brand', 'model']
    
    phone_issue_keywords = ['spoil', 'broken', 'damage', 'malfunction', 'battery', 'slow', 'lag', 'hang', 
                           'old phone', 'dying', 'issue', 'problem', 'not working', 'outdated']
    
    contract_keywords = ['contract', 'recontract', 're-contract', 'renewal', 'renew', 'end', 'ending', 
                        'expire', 'due', 'overdue']
    
    provider_keywords = ['switch provider', 'change provider', 'from singtel', 'from starhub', 
                        'cheaper than', 'better plan']
    
    # Check categories in order of priority
    if any(keyword in motivation_text for keyword in promotion_keywords):
        return 'Promotion'
    elif any(keyword in motivation_text for keyword in contract_keywords):
        return 'Contract ending'
    elif any(keyword in motivation_text for keyword in phone_issue_keywords):
        return 'Phone issue'
    elif any(keyword in motivation_text for keyword in upgrade_keywords):
        return 'Upgrade of phone'
    elif any(keyword in motivation_text for keyword in provider_keywords):
        return 'Switch Provider'
    else:
        return 'Others'

def load_data():
    """Load survey data with proper encoding"""
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open('data-exploration-export_Device_wave2.csv', 'r', encoding=encoding) as file:
                csv_reader = csv.reader(file)
                headers = next(csv_reader)
                data = list(csv_reader)
            return data, headers
        except UnicodeDecodeError:
            continue
    
    raise Exception("Could not read file with any encoding")

def analyze_motivations(data, headers):
    """Analyze customer motivations (Result 1)"""
    motivation_col_index = None
    for i, header in enumerate(headers):
        if 'Motivation to Purchase' in header:
            motivation_col_index = i
            break
    
    if motivation_col_index is None:
        return None
    
    categories = []
    for row in data:
        if len(row) > motivation_col_index:
            motivation_text = row[motivation_col_index]
            category = categorize_motivation(motivation_text)
            categories.append(category)
    
    return Counter(categories)

def analyze_campaign_awareness(data, headers):
    """Analyze M1 Anniversary Campaign awareness (Result 2)"""
    campaign_col_index = None
    for i, header in enumerate(headers):
        if 'Were you aware of the M1 Anniversary Campaign?' in header:
            campaign_col_index = i
            break
    
    if campaign_col_index is None:
        return None
    
    responses = []
    for row in data:
        if len(row) > campaign_col_index and row[campaign_col_index].strip():
            responses.append(row[campaign_col_index].strip())
    
    return Counter(responses)

def analyze_device_types(data, headers):
    """Analyze device type preferences (Result 3)"""
    device_col_index = None
    for i, header in enumerate(headers):
        if 'Device Type' in header:
            device_col_index = i
            break
    
    if device_col_index is None:
        return None
    
    devices = []
    for row in data:
        if len(row) > device_col_index and row[device_col_index].strip():
            devices.append(row[device_col_index].strip())
    
    return Counter(devices)

def analyze_age_demographics(data, headers):
    """Analyze age demographics (Result 4)"""
    age_col_index = None
    for i, header in enumerate(headers):
        if 'Age Band' in header:
            age_col_index = i
            break
    
    if age_col_index is None:
        return None
    
    ages = []
    for row in data:
        if len(row) > age_col_index and row[age_col_index].strip():
            ages.append(row[age_col_index].strip())
    
    return Counter(ages)

def generate_insights(motivation_counts, campaign_counts, device_counts, age_counts):
    """Generate insights and recommendations"""
    
    total_responses = sum(motivation_counts.values())
    
    insights = []
    
    # Motivation insights
    promotion_pct = (motivation_counts['Promotion'] / total_responses) * 100
    insights.append(f"• {promotion_pct:.1f}% of customers were motivated by promotions/deals, making it the top driver")
    
    phone_issue_pct = (motivation_counts['Phone issue'] / total_responses) * 100
    insights.append(f"• {phone_issue_pct:.1f}% upgraded due to phone issues, indicating natural replacement cycle")
    
    # Campaign awareness insights
    if campaign_counts:
        aware_pct = (campaign_counts['Yes'] / sum(campaign_counts.values())) * 100
        insights.append(f"• {aware_pct:.1f}% were aware of the M1 Anniversary Campaign")
    
    # Device preference insights
    if device_counts:
        iphone_pct = (device_counts['IPhone'] / sum(device_counts.values())) * 100
        insights.append(f"• {iphone_pct:.1f}% chose iPhone, showing strong Apple preference")
    
    # Age demographic insights
    if age_counts:
        mature_ages = ['45-&lt;50', '50-&lt;55', '55-&lt;60', '60-&lt;65']
        mature_count = sum(age_counts.get(age, 0) for age in mature_ages)
        mature_pct = (mature_count / sum(age_counts.values())) * 100
        insights.append(f"• {mature_pct:.1f}% of customers are in the 45-65 age range (mature segment)")
    
    return insights

def main():
    """Generate comprehensive analysis report"""
    
    print("="*80)
    print("M1 DEVICE PURCHASE BEHAVIOR SURVEY - COMPREHENSIVE ANALYSIS REPORT")
    print("Anniversary Campaign Period: April - Mid June")
    print("="*80)
    
    # Load data
    data, headers = load_data()
    total_responses = len(data)
    
    print(f"\nSurvey Overview:")
    print(f"• Total Responses: {total_responses}")
    print(f"• Survey Period: Anniversary Campaign (Apr-mid June)")
    print(f"• Target: Postpaid customers who purchased devices during campaign")
    
    # Analyze all metrics
    motivation_counts = analyze_motivations(data, headers)
    campaign_counts = analyze_campaign_awareness(data, headers)
    device_counts = analyze_device_types(data, headers)
    age_counts = analyze_age_demographics(data, headers)
    
    # Result 1: Motivation Analysis
    print(f"\n{'='*60}")
    print("RESULT 1: MAIN MOTIVATION FOR PHONE UPGRADE")
    print("="*60)
    
    if motivation_counts:
        for category, count in motivation_counts.most_common():
            percentage = (count / total_responses) * 100
            print(f"{category:<20}: {count:>4} ({percentage:>5.1f}%)")
    
    # Result 2: Campaign Awareness
    print(f"\n{'='*60}")
    print("RESULT 2: M1 ANNIVERSARY CAMPAIGN AWARENESS")
    print("="*60)
    
    if campaign_counts:
        for response, count in campaign_counts.most_common():
            percentage = (count / sum(campaign_counts.values())) * 100
            print(f"{response:<10}: {count:>4} ({percentage:>5.1f}%)")
    
    # Result 3: Device Preferences
    print(f"\n{'='*60}")
    print("RESULT 3: DEVICE TYPE PREFERENCES")
    print("="*60)
    
    if device_counts:
        for device, count in device_counts.most_common():
            percentage = (count / sum(device_counts.values())) * 100
            print(f"{device:<15}: {count:>4} ({percentage:>5.1f}%)")
    
    # Result 4: Age Demographics
    print(f"\n{'='*60}")
    print("RESULT 4: AGE DEMOGRAPHICS")
    print("="*60)
    
    if age_counts:
        for age, count in age_counts.most_common():
            percentage = (count / sum(age_counts.values())) * 100
            print(f"{age:<15}: {count:>4} ({percentage:>5.1f}%)")
    
    # Generate insights and recommendations
    insights = generate_insights(motivation_counts, campaign_counts, device_counts, age_counts)
    
    print(f"\n{'='*60}")
    print("KEY INSIGHTS & FINDINGS")
    print("="*60)
    
    for insight in insights:
        print(insight)
    
    print(f"\n{'='*60}")
    print("RECOMMENDATIONS")
    print("="*60)
    
    recommendations = [
        "1. PROMOTION STRATEGY: Continue strong promotional campaigns as 27.4% are motivated by deals",
        "2. TRADE-IN PROGRAMS: Expand trade-in offers as they're highly effective for customer acquisition",
        "3. IPHONE FOCUS: Prioritize iPhone inventory and promotions given 61.7% preference",
        "4. MATURE MARKET: Target 45-65 age group with tailored messaging and offers",
        "5. CAMPAIGN AWARENESS: Improve campaign visibility as 41.1% were unaware of anniversary campaign",
        "6. RETENTION STRATEGY: Address phone issues proactively to prevent churn (18.1% upgrade due to issues)"
    ]
    
    for rec in recommendations:
        print(rec)
    
    # Save detailed results
    results = {
        'survey_overview': {
            'total_responses': total_responses,
            'period': 'April - Mid June (Anniversary Campaign)'
        },
        'motivation_distribution': dict(motivation_counts),
        'campaign_awareness': dict(campaign_counts) if campaign_counts else {},
        'device_preferences': dict(device_counts) if device_counts else {},
        'age_demographics': dict(age_counts) if age_counts else {},
        'insights': insights,
        'recommendations': recommendations
    }
    
    with open('comprehensive_analysis_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n{'='*60}")
    print("ANALYSIS COMPLETE")
    print("="*60)
    print("• Detailed results saved to 'comprehensive_analysis_results.json'")
    print("• All 4 required result analyses completed")
    print("• Additional insights and recommendations provided")

if __name__ == "__main__":
    main()
