<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M1 Survey Analysis - Enhanced Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chart-wrapper {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .wordcloud-wrapper {
            position: relative;
            height: 400px;
            margin: 20px 0;
            border: 1px solid #ddd;
            padding: 10px;
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .summary {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .insight {
            background: #f0f8e8;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }
        .regression-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .regression-table th, .regression-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .regression-table th {
            background-color: #007AFF;
            color: white;
        }
        .two-column {
            display: flex;
            gap: 20px;
        }
        .column {
            flex: 1;
        }
    </style>
</head>
<body>
    <h1>M1 Device Purchase Behavior Survey - Enhanced Analysis</h1>
    <div class="summary">
        <h3>Complete Requirements Analysis</h3>
        <p><strong>✓ Q3 Enhanced Categorization:</strong> Free-text motivation analysis with subcategories</p>
        <p><strong>✓ Research Behavior (Q5, 5a, 5b):</strong> How customers research before purchase</p>
        <p><strong>✓ Demographic Slicing:</strong> Age, Order Type, Plan Type, Channel, Tenure analysis</p>
        <p><strong>✓ Regression Analysis:</strong> Demographic influences on key behaviors</p>
        <p><strong>✓ Cross-tabulations:</strong> Age vs Order Type, Age vs Channel</p>
        <p><strong>✓ Word Cloud:</strong> Visual analysis of "Others" category responses</p>
    </div>

    <!-- Enhanced Q3 Analysis -->
    <div class="chart-container">
        <h2>Enhanced Q3: Motivation Analysis with Subcategories</h2>
        <div class="two-column">
            <div class="column">
                <div class="chart-wrapper">
                    <canvas id="motivationChart"></canvas>
                </div>
            </div>
            <div class="column">
                <div class="chart-wrapper">
                    <canvas id="othersSubcategoryChart"></canvas>
                </div>
            </div>
        </div>
        <div class="insight">
            <strong>Enhanced Insight:</strong> "Others" category (26.3%) breaks down into General/Unspecified (67.2%), 
            Short/Unclear responses (26.8%), and specific needs like gifts (2.5%) and work requirements (1.8%)
        </div>
    </div>

    <!-- Word Cloud for Others -->
    <div class="chart-container">
        <h2>Word Cloud: "Others" Category Analysis</h2>
        <div class="wordcloud-wrapper">
            <canvas id="wordcloudChart" width="800" height="300"></canvas>
        </div>
        <div class="insight">
            <strong>Word Cloud Insight:</strong> Top words in "Others" responses: phone (114), good (43), old (35), price (33), new (31), iPhone (25) -
            indicating general satisfaction and replacement needs
        </div>
    </div>

    <!-- Research Behavior Analysis -->
    <div class="chart-container">
        <h2>Research Behavior Analysis (Q5, 5a, 5b)</h2>
        <div class="two-column">
            <div class="column">
                <div class="chart-wrapper">
                    <canvas id="considerationChart"></canvas>
                </div>
            </div>
            <div class="column">
                <div class="chart-wrapper">
                    <canvas id="searchPreferenceChart"></canvas>
                </div>
            </div>
        </div>
        <div class="insight">
            <strong>Research Insight:</strong> 71.2% compare offers, 36% take 1-6 months to decide, 
            40.9% prefer online research vs 19.6% prefer physical stores
        </div>
    </div>

    <!-- Demographic Influences -->
    <div class="chart-container">
        <h2>Regression Analysis: Demographic Influences</h2>
        <h3>Promotion Motivation by Demographics</h3>
        <table class="regression-table">
            <thead>
                <tr><th>Demographic</th><th>Segment</th><th>Promotion Motivation Rate</th></tr>
            </thead>
            <tbody>
                <tr><td>Age</td><td>30-35 years</td><td>34.4%</td></tr>
                <tr><td>Age</td><td>65-70 years</td><td>33.3%</td></tr>
                <tr><td>Order Type</td><td>GA (New)</td><td>33.8%</td></tr>
                <tr><td>Tenure</td><td>2-6 years</td><td>36.3%</td></tr>
                <tr><td>Channel</td><td>Online (Ship)</td><td>28.6%</td></tr>
            </tbody>
        </table>
        
        <h3>Campaign Awareness by Demographics</h3>
        <table class="regression-table">
            <thead>
                <tr><th>Demographic</th><th>Segment</th><th>Campaign Awareness Rate</th></tr>
            </thead>
            <tbody>
                <tr><td>Age</td><td>65-70 years</td><td>66.7%</td></tr>
                <tr><td>Age</td><td>50-55 years</td><td>64.4%</td></tr>
                <tr><td>Order Type</td><td>Upgrades</td><td>61.7%</td></tr>
                <tr><td>Plan Type</td><td>Bespoke Flexi</td><td>62.1%</td></tr>
                <tr><td>Channel</td><td>Online (Ship)</td><td>64.0%</td></tr>
            </tbody>
        </table>
    </div>

    <!-- Result 3: Phone Purchased for Whom -->
    <div class="chart-container">
        <h2>Result 3: Phone Purchased for Whom</h2>
        <div class="chart-wrapper">
            <canvas id="purchaseForChart"></canvas>
        </div>
        <div class="insight">
            <strong>Purchase Target Insight:</strong> 79.3% purchase for themselves, 10.4% for spouse/partner,
            6.5% for children - indicating primarily personal device upgrades
        </div>
    </div>

    <!-- Result 4: Factors Influencing Purchase Decision -->
    <div class="chart-container">
        <h2>Result 4: Top Factors Influencing Purchase Decision</h2>
        <div class="chart-wrapper">
            <canvas id="influencingFactorsChart"></canvas>
        </div>
        <div class="insight">
            <strong>Decision Factors Insight:</strong> Price (4.2%) is the top specific factor mentioned,
            followed by general responses (Nil, NA). Many responses indicate promotion-related factors and contract timing.
        </div>
    </div>

    <!-- Cross-tabulation Analysis -->
    <div class="chart-container">
        <h2>Cross-tabulation: Age vs Order Type</h2>
        <div class="chart-wrapper">
            <canvas id="crossTabChart"></canvas>
        </div>
        <div class="insight">
            <strong>Cross-tab Insight:</strong> Younger customers (21-30) prefer GA (new acquisitions),
            while mature customers (45+) predominantly do Re-Contracts
        </div>
    </div>

    <div class="summary">
        <h3>Strategic Recommendations Based on Enhanced Analysis</h3>
        <ol>
            <li><strong>Target Young Adults (30-35):</strong> Highest promotion motivation (34.4%) - focus promotional campaigns here</li>
            <li><strong>Leverage Online Channels:</strong> Online customers show higher campaign awareness (64% vs 57.6%)</li>
            <li><strong>Improve Campaign Reach:</strong> Younger segments (21-30) have lower awareness - enhance digital marketing</li>
            <li><strong>Optimize Research Journey:</strong> 71% compare offers, 40% prefer online research - strengthen online presence</li>
            <li><strong>Segment-Specific Messaging:</strong> GA customers more promotion-sensitive, Re-Contract customers need retention focus</li>
            <li><strong>Address "Others" Category:</strong> 26% unclear motivations suggest need for better customer journey understanding</li>
        </ol>
    </div>

    <script>
        // Chart.js configuration
        Chart.defaults.font.size = 11;
        Chart.defaults.color = '#333';

        // Enhanced Motivation Chart
        const motivationCtx = document.getElementById('motivationChart').getContext('2d');
        new Chart(motivationCtx, {
            type: 'bar',
            data: {
                labels: ['Promotion', 'Others', 'Phone issue', 'Upgrade of phone', 'Contract ending'],
                datasets: [{
                    label: 'Number of Customers',
                    data: [458, 436, 312, 238, 215],
                    backgroundColor: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
                    borderColor: '#333',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Main Motivation Categories' },
                    legend: { display: false }
                }
            }
        });

        // Others Subcategory Chart
        const othersCtx = document.getElementById('othersSubcategoryChart').getContext('2d');
        new Chart(othersCtx, {
            type: 'doughnut',
            data: {
                labels: ['General/Unspecified', 'Short/Unclear', 'Gift/Special', 'Work/Business', 'Family', 'Convenience'],
                datasets: [{
                    data: [293, 117, 11, 8, 5, 2],
                    backgroundColor: ['#FF9999', '#66B2FF', '#99FF99', '#FFCC99', '#FF99CC', '#99CCFF']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: '"Others" Category Breakdown' }
                }
            }
        });

        // Consideration Length Chart
        const considerationCtx = document.getElementById('considerationChart').getContext('2d');
        new Chart(considerationCtx, {
            type: 'pie',
            data: {
                labels: ['1-6 months', '<1 month', '>1 year', '6m-1 year'],
                datasets: [{
                    data: [597, 510, 324, 228],
                    backgroundColor: ['#36A2EB', '#FF6384', '#FFCE56', '#4BC0C0']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Research Consideration Length' }
                }
            }
        });

        // Search Preference Chart
        const searchCtx = document.getElementById('searchPreferenceChart').getContext('2d');
        new Chart(searchCtx, {
            type: 'bar',
            data: {
                labels: ['Online Only', 'Both Online & Physical', 'Physical Only'],
                datasets: [{
                    label: 'Customers',
                    data: [492, 475, 235],
                    backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Research Preferences' },
                    legend: { display: false }
                }
            }
        });

        // Cross-tabulation Chart
        const crossTabCtx = document.getElementById('crossTabChart').getContext('2d');
        new Chart(crossTabCtx, {
            type: 'bar',
            data: {
                labels: ['21-25', '25-30', '30-35', '35-40', '40-45', '45-50', '50-55', '55-60', '60-65', '65-70', '>=70'],
                datasets: [
                    {
                        label: 'GA',
                        data: [64, 54, 30, 21, 17, 13, 13, 15, 18, 11, 16],
                        backgroundColor: '#FF6384'
                    },
                    {
                        label: 'Re-Contract',
                        data: [16, 28, 48, 52, 58, 59, 62, 54, 55, 57, 45],
                        backgroundColor: '#36A2EB'
                    },
                    {
                        label: 'Upgrades',
                        data: [20, 18, 22, 27, 26, 27, 25, 31, 28, 32, 39],
                        backgroundColor: '#FFCE56'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Age vs Order Type Distribution (%)' }
                },
                scales: {
                    x: { stacked: true },
                    y: { stacked: true, max: 100 }
                }
            }
        });

        // Result 3: Phone Purchased for Whom Chart
        const purchaseForCtx = document.getElementById('purchaseForChart').getContext('2d');
        new Chart(purchaseForCtx, {
            type: 'doughnut',
            data: {
                labels: ['Myself', 'My spouse/partner', 'My kid', 'My parent', 'Others'],
                datasets: [{
                    data: [1315, 172, 108, 35, 28],
                    backgroundColor: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
                    borderColor: '#333',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Phone Purchase Target Distribution' },
                    legend: { position: 'right' }
                }
            }
        });

        // Result 4: Top Factors Influencing Purchase Decision Chart
        const influencingFactorsCtx = document.getElementById('influencingFactorsChart').getContext('2d');
        new Chart(influencingFactorsCtx, {
            type: 'bar',
            data: {
                labels: ['Price', 'Nil', 'NA', 'Cost', 'Na', 'Promotion price', 'Good', 'No comment', 'Contract end', 'Price and promotion'],
                datasets: [{
                    label: 'Number of Mentions',
                    data: [20, 18, 6, 5, 4, 3, 2, 2, 2, 2],
                    backgroundColor: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'],
                    borderColor: '#333',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Top 10 Factors Influencing Purchase Decision' },
                    legend: { display: false }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Number of Mentions' }
                    },
                    x: {
                        title: { display: true, text: 'Factors' }
                    }
                }
            }
        });

        // Word Cloud using Chart.js as a bar chart (more reliable)
        const wordCloudCtx = document.getElementById('wordcloudChart').getContext('2d');
        const wordCloudData = [
            {word: 'phone', count: 114}, {word: 'good', count: 43}, {word: 'old', count: 35},
            {word: 'price', count: 33}, {word: 'new', count: 31}, {word: 'iphone', count: 25},
            {word: 'need', count: 19}, {word: 'nil', count: 18}, {word: 'previous', count: 17},
            {word: 'years', count: 16}, {word: 'screen', count: 13}, {word: 'time', count: 12},
            {word: 'more', count: 11}, {word: 'needed', count: 10}, {word: 'very', count: 10}
        ];

        new Chart(wordCloudCtx, {
            type: 'bar',
            data: {
                labels: wordCloudData.map(item => item.word),
                datasets: [{
                    label: 'Word Frequency',
                    data: wordCloudData.map(item => item.count),
                    backgroundColor: [
                        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
                        '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
                    ],
                    borderColor: '#333',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    title: {
                        display: true,
                        text: 'Top Words in "Others" Category Responses',
                        font: { size: 14 }
                    },
                    legend: { display: false }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Frequency'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Words'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
