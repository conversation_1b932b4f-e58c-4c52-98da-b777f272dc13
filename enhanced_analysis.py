#!/usr/bin/env python3
"""
Enhanced M1 Survey Analysis - Complete Requirements Implementation
Includes all missing requirements: demographics analysis, regression, word cloud, enhanced categorization
"""

import csv
import re
from collections import Counter, defaultdict
import json
import math

def load_data():
    """Load survey data with proper encoding"""
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open('data-exploration-export_Device_wave2.csv', 'r', encoding=encoding) as file:
                csv_reader = csv.reader(file)
                headers = next(csv_reader)
                data = list(csv_reader)
            return data, headers
        except UnicodeDecodeError:
            continue
    
    raise Exception("Could not read file with any encoding")

def get_column_index(headers, search_terms):
    """Find column index by searching for terms in headers"""
    for i, header in enumerate(headers):
        for term in search_terms:
            if term.lower() in header.lower():
                return i
    return None

def categorize_motivation_enhanced(motivation_text):
    """Enhanced categorization with better 'Others' breakdown"""
    if not motivation_text or motivation_text.strip() == '':
        return 'Others', 'Empty/No Response'
    
    motivation_text = str(motivation_text).lower().strip()
    
    # Define keywords for each category
    promotion_keywords = ['promotion', 'promo', 'discount', 'deal', 'offer', 'anniversary', 'trade in', 'trade-in', 
                         'rebate', 'free', 'attractive', 'good deal', 'great deal', 'birthday', 'campaign', 'cheaper']
    
    upgrade_keywords = ['upgrade', 'new phone', 'latest', 'better', 'newer', 'technology', 'features', 
                       'camera', 'storage', 'performance', 'swift', 'switch brand', 'model', 'advanced']
    
    phone_issue_keywords = ['spoil', 'broken', 'damage', 'malfunction', 'battery', 'slow', 'lag', 'hang', 
                           'old phone', 'dying', 'issue', 'problem', 'not working', 'outdated', 'faulty']
    
    contract_keywords = ['contract', 'recontract', 're-contract', 'renewal', 'renew', 'end', 'ending', 
                        'expire', 'due', 'overdue', 'term']
    
    provider_keywords = ['switch provider', 'change provider', 'from singtel', 'from starhub', 
                        'cheaper than', 'better plan', 'change telco']
    
    # Enhanced Others subcategories
    gift_keywords = ['gift', 'present', 'birthday', 'christmas', 'anniversary', 'surprise']
    work_keywords = ['work', 'business', 'office', 'company', 'professional']
    family_keywords = ['family', 'children', 'kids', 'parents', 'spouse', 'wife', 'husband']
    convenience_keywords = ['convenient', 'easy', 'simple', 'hassle-free']
    
    # Check main categories first
    if any(keyword in motivation_text for keyword in promotion_keywords):
        return 'Promotion', 'Promotion/Deal'
    elif any(keyword in motivation_text for keyword in contract_keywords):
        return 'Contract ending', 'Contract Related'
    elif any(keyword in motivation_text for keyword in phone_issue_keywords):
        return 'Phone issue', 'Technical Issues'
    elif any(keyword in motivation_text for keyword in upgrade_keywords):
        return 'Upgrade of phone', 'Technology Upgrade'
    elif any(keyword in motivation_text for keyword in provider_keywords):
        return 'Switch Provider', 'Provider Change'
    else:
        # Enhanced Others categorization
        if any(keyword in motivation_text for keyword in gift_keywords):
            return 'Others', 'Gift/Special Occasion'
        elif any(keyword in motivation_text for keyword in work_keywords):
            return 'Others', 'Work/Business Need'
        elif any(keyword in motivation_text for keyword in family_keywords):
            return 'Others', 'Family Related'
        elif any(keyword in motivation_text for keyword in convenience_keywords):
            return 'Others', 'Convenience'
        elif len(motivation_text) < 10:
            return 'Others', 'Short/Unclear Response'
        else:
            return 'Others', 'General/Unspecified'

def analyze_research_behavior(data, headers):
    """Analyze Q5, 5a, 5b - research behavior"""
    results = {}
    
    # Q5: Consideration Length
    consideration_col = get_column_index(headers, ['Consideration Length'])
    if consideration_col:
        consideration_data = [row[consideration_col] for row in data if len(row) > consideration_col and row[consideration_col].strip()]
        results['consideration_length'] = Counter(consideration_data)
    
    # Q5a: Compared offers and pricing
    compared_col = get_column_index(headers, ['Compared offers and pricing'])
    if compared_col:
        compared_data = [row[compared_col] for row in data if len(row) > compared_col and row[compared_col].strip()]
        results['compared_offers'] = Counter(compared_data)
    
    # Q5b: Search Preferences
    search_pref_col = get_column_index(headers, ['Search Preferences'])
    if search_pref_col:
        search_data = [row[search_pref_col] for row in data if len(row) > search_pref_col and row[search_pref_col].strip()]
        results['search_preferences'] = Counter(search_data)
    
    return results

def get_demographics_data(data, headers):
    """Extract demographics data for slicing"""
    demographics = {}
    
    # Age Band (Col B equivalent)
    age_col = get_column_index(headers, ['Age Band'])
    if age_col:
        demographics['age_band'] = [row[age_col] if len(row) > age_col else '' for row in data]
    
    # Order Subtype (GA/Recon/SIMO)
    order_col = get_column_index(headers, ['Order Subtype'])
    if order_col:
        demographics['order_subtype'] = [row[order_col] if len(row) > order_col else '' for row in data]
    
    # Plan type (BP_L2)
    plan_col = get_column_index(headers, ['BP Level2', 'BP_L2'])
    if plan_col:
        demographics['plan_type'] = [row[plan_col] if len(row) > plan_col else '' for row in data]
    
    # Purchase channel (retail/online)
    channel_col = get_column_index(headers, ['Order Fulfillment Channel'])
    if channel_col:
        demographics['purchase_channel'] = [row[channel_col] if len(row) > channel_col else '' for row in data]
    
    # Line tenure (need to band it)
    tenure_col = get_column_index(headers, ['Line Tenure'])
    if tenure_col:
        raw_tenure = [row[tenure_col] if len(row) > tenure_col else '' for row in data]
        demographics['line_tenure'] = [band_tenure(t) for t in raw_tenure]
    
    return demographics

def band_tenure(tenure_str):
    """Band line tenure into specified ranges"""
    try:
        tenure = float(tenure_str) if tenure_str else 0
        if tenure < 2:
            return '<2yrs'
        elif tenure < 6:
            return '2-<6yrs'
        elif tenure < 10:
            return '6-<10yrs'
        elif tenure < 20:
            return '10-<20yrs'
        else:
            return '>20yrs'
    except:
        return 'Unknown'

def create_word_cloud_data(others_responses):
    """Create word cloud data from 'Others' responses"""
    word_freq = Counter()
    
    # Common stop words to exclude
    stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an', 
                  'is', 'was', 'are', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
                  'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'i', 'you', 'he', 
                  'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 
                  'her', 'its', 'our', 'their', 'this', 'that', 'these', 'those', 'ok', 'okay'}
    
    for response in others_responses:
        if response and response.strip():
            # Clean and split text
            words = re.findall(r'\b[a-zA-Z]{3,}\b', response.lower())
            for word in words:
                if word not in stop_words:
                    word_freq[word] += 1
    
    return word_freq

def demographic_slice_analysis(data, headers, demographics):
    """Perform demographic slicing analysis"""
    results = {}
    
    # Get key columns
    motivation_col = get_column_index(headers, ['Motivation to Purchase'])
    campaign_aware_col = get_column_index(headers, ['Were you aware of the M1 Anniversary Campaign'])
    purchase_for_col = get_column_index(headers, ['Phone Purchased for Whom'])
    consideration_col = get_column_index(headers, ['Consideration Length'])
    
    # Slice by Age Band
    age_slices = defaultdict(lambda: defaultdict(Counter))
    
    for i, row in enumerate(data):
        age = demographics['age_band'][i] if i < len(demographics['age_band']) else ''
        if not age:
            continue
            
        # Motivation by age
        if motivation_col and len(row) > motivation_col:
            main_cat, _ = categorize_motivation_enhanced(row[motivation_col])
            age_slices[age]['motivation'][main_cat] += 1
        
        # Campaign awareness by age
        if campaign_aware_col and len(row) > campaign_aware_col:
            age_slices[age]['campaign_awareness'][row[campaign_aware_col]] += 1
        
        # Purchase for whom by age
        if purchase_for_col and len(row) > purchase_for_col:
            age_slices[age]['purchase_for'][row[purchase_for_col]] += 1
        
        # Research time by age
        if consideration_col and len(row) > consideration_col:
            age_slices[age]['consideration_length'][row[consideration_col]] += 1
    
    results['age_slices'] = dict(age_slices)
    
    # Similar analysis for other demographics
    # Order Subtype slices
    order_slices = defaultdict(lambda: defaultdict(Counter))
    for i, row in enumerate(data):
        order_type = demographics['order_subtype'][i] if i < len(demographics['order_subtype']) else ''
        if not order_type:
            continue
            
        if motivation_col and len(row) > motivation_col:
            main_cat, _ = categorize_motivation_enhanced(row[motivation_col])
            order_slices[order_type]['motivation'][main_cat] += 1
    
    results['order_slices'] = dict(order_slices)
    
    return results

def summary_demographics(demographics):
    """Generate summary statistics for demographics"""
    summary = {}
    
    for demo_type, demo_data in demographics.items():
        if demo_data:
            summary[demo_type] = Counter([d for d in demo_data if d])
    
    return summary

def cross_tabulation(demographics):
    """Create cross-tabulations"""
    cross_tabs = {}
    
    # Age vs Order Subtype
    age_order_cross = defaultdict(Counter)
    for i in range(len(demographics.get('age_band', []))):
        age = demographics['age_band'][i] if i < len(demographics['age_band']) else ''
        order = demographics['order_subtype'][i] if i < len(demographics['order_subtype']) else ''
        if age and order:
            age_order_cross[age][order] += 1
    
    cross_tabs['age_vs_order'] = dict(age_order_cross)
    
    # Age vs Purchase Channel
    age_channel_cross = defaultdict(Counter)
    for i in range(len(demographics.get('age_band', []))):
        age = demographics['age_band'][i] if i < len(demographics['age_band']) else ''
        channel = demographics['purchase_channel'][i] if i < len(demographics['purchase_channel']) else ''
        if age and channel:
            age_channel_cross[age][channel] += 1
    
    cross_tabs['age_vs_channel'] = dict(age_channel_cross)
    
    return cross_tabs

def simple_regression_analysis(data, headers, demographics):
    """Perform simple regression-like analysis to identify demographic influences"""

    # Get key outcome variables
    motivation_col = get_column_index(headers, ['Motivation to Purchase'])
    campaign_aware_col = get_column_index(headers, ['Were you aware of the M1 Anniversary Campaign'])
    purchase_for_col = get_column_index(headers, ['Phone Purchased for Whom'])
    consideration_col = get_column_index(headers, ['Consideration Length'])

    # Channel awareness analysis (Q7a)
    channel_cols = {}
    awareness_channels = ['TV ads', 'Online ads', 'Radio ads', 'Social Media Post', 'E-mailer',
                         'M1 website', 'SMS', 'M1 App', 'Word of mouth']

    for channel in awareness_channels:
        col_idx = get_column_index(headers, [channel])
        if col_idx:
            channel_cols[channel] = col_idx

    regression_results = {}

    # Analyze influence of demographics on key outcomes
    for demo_type, demo_values in demographics.items():
        if not demo_values:
            continue

        demo_influence = {
            'motivation_promotion': {},
            'campaign_awareness': {},
            'research_time_long': {},
            'awareness_channels': {}
        }

        # Group by demographic value
        demo_groups = defaultdict(list)
        for i, demo_val in enumerate(demo_values):
            if demo_val and i < len(data):
                demo_groups[demo_val].append(i)

        # Analyze each demographic group
        for demo_val, indices in demo_groups.items():
            if len(indices) < 10:  # Skip small groups
                continue

            group_size = len(indices)

            # 1. Promotion motivation rate
            promotion_count = 0
            for idx in indices:
                if len(data[idx]) > motivation_col:
                    main_cat, _ = categorize_motivation_enhanced(data[idx][motivation_col])
                    if main_cat == 'Promotion':
                        promotion_count += 1
            demo_influence['motivation_promotion'][demo_val] = promotion_count / group_size * 100

            # 2. Campaign awareness rate
            aware_count = 0
            for idx in indices:
                if len(data[idx]) > campaign_aware_col and data[idx][campaign_aware_col] == 'Yes':
                    aware_count += 1
            demo_influence['campaign_awareness'][demo_val] = aware_count / group_size * 100

            # 3. Long research time rate (>6 months)
            long_research_count = 0
            for idx in indices:
                if len(data[idx]) > consideration_col:
                    consideration = data[idx][consideration_col]
                    if 'year' in consideration.lower() or '6 months to 1 year' in consideration:
                        long_research_count += 1
            demo_influence['research_time_long'][demo_val] = long_research_count / group_size * 100

            # 4. Channel awareness patterns
            channel_awareness = {}
            for channel, col_idx in channel_cols.items():
                channel_count = 0
                for idx in indices:
                    if len(data[idx]) > col_idx and data[idx][col_idx].strip().lower() == 'yes':
                        channel_count += 1
                channel_awareness[channel] = channel_count / group_size * 100
            demo_influence['awareness_channels'][demo_val] = channel_awareness

        regression_results[demo_type] = demo_influence

    return regression_results

def main():
    """Enhanced comprehensive analysis"""
    print("="*80)
    print("M1 DEVICE PURCHASE BEHAVIOR SURVEY - ENHANCED COMPREHENSIVE ANALYSIS")
    print("="*80)

    # Load data
    data, headers = load_data()
    total_responses = len(data)
    
    print(f"\nSurvey Overview: {total_responses:,} responses")
    
    # 1. Enhanced Q3 Analysis with word cloud and better categorization
    print(f"\n{'='*60}")
    print("ENHANCED Q3 ANALYSIS: MOTIVATION CATEGORIZATION")
    print("="*60)
    
    motivation_col = get_column_index(headers, ['Motivation to Purchase'])
    enhanced_motivations = []
    others_responses = []
    others_subcategories = Counter()
    
    for row in data:
        if len(row) > motivation_col:
            main_cat, sub_cat = categorize_motivation_enhanced(row[motivation_col])
            enhanced_motivations.append(main_cat)
            if main_cat == 'Others':
                others_responses.append(row[motivation_col])
                others_subcategories[sub_cat] += 1
    
    motivation_counts = Counter(enhanced_motivations)
    
    print("Main Categories:")
    for category, count in motivation_counts.most_common():
        percentage = (count / total_responses) * 100
        print(f"{category:<20}: {count:>4} ({percentage:>5.1f}%)")
    
    print(f"\nOthers Subcategory Breakdown:")
    for subcat, count in others_subcategories.most_common():
        percentage = (count / motivation_counts['Others']) * 100
        print(f"  {subcat:<25}: {count:>3} ({percentage:>5.1f}%)")
    
    # Word cloud data for Others
    word_cloud_data = create_word_cloud_data(others_responses)
    print(f"\nTop Words in 'Others' Responses (for word cloud):")
    for word, freq in word_cloud_data.most_common(20):
        print(f"  {word}: {freq}")
    
    # 2. Research Behavior Analysis (Q5, 5a, 5b)
    print(f"\n{'='*60}")
    print("RESEARCH BEHAVIOR ANALYSIS (Q5, 5a, 5b)")
    print("="*60)
    
    research_data = analyze_research_behavior(data, headers)
    
    for analysis_type, results in research_data.items():
        print(f"\n{analysis_type.replace('_', ' ').title()}:")
        total = sum(results.values())
        for item, count in results.most_common():
            percentage = (count / total) * 100 if total > 0 else 0
            print(f"  {item:<30}: {count:>4} ({percentage:>5.1f}%)")
    
    # 3. Demographics Analysis
    print(f"\n{'='*60}")
    print("DEMOGRAPHICS ANALYSIS")
    print("="*60)
    
    demographics = get_demographics_data(data, headers)
    demo_summary = summary_demographics(demographics)
    
    for demo_type, counts in demo_summary.items():
        print(f"\n{demo_type.replace('_', ' ').title()}:")
        total = sum(counts.values())
        for item, count in counts.most_common():
            percentage = (count / total) * 100 if total > 0 else 0
            print(f"  {item:<25}: {count:>4} ({percentage:>5.1f}%)")
    
    # 4. Demographic Slicing
    print(f"\n{'='*60}")
    print("DEMOGRAPHIC SLICING ANALYSIS")
    print("="*60)
    
    slice_results = demographic_slice_analysis(data, headers, demographics)
    
    # Show key slices
    print("\nMotivation by Age Band:")
    for age, metrics in slice_results['age_slices'].items():
        if 'motivation' in metrics:
            total = sum(metrics['motivation'].values())
            print(f"\n{age} (n={total}):")
            for motivation, count in metrics['motivation'].most_common(3):
                pct = (count / total) * 100 if total > 0 else 0
                print(f"  {motivation}: {pct:.1f}%")
    
    # 5. Cross-tabulations
    print(f"\n{'='*60}")
    print("CROSS-TABULATION ANALYSIS")
    print("="*60)
    
    cross_tabs = cross_tabulation(demographics)
    
    print("\nAge vs Order Subtype:")
    for age, order_counts in cross_tabs['age_vs_order'].items():
        total = sum(order_counts.values())
        print(f"{age:<15}: ", end="")
        for order_type, count in order_counts.most_common():
            pct = (count / total) * 100
            print(f"{order_type}({pct:.0f}%) ", end="")
        print()
    
    # 6. Result 3: Rating Factors Analysis - "Please rate the following factors that would influence your choice when buying a new phone"
    print(f"\n{'='*60}")
    print("RESULT 3: RATING FACTORS INFLUENCING PHONE PURCHASE CHOICE")
    print("="*60)

    # Define the rating factor columns (columns 33-43 in the CSV)
    rating_factors = [
        'Upfront price of phone at $0',
        'New & Port-in discounts or loyalty discounts',
        'M1 Anniversary promotion',
        'Free Premiums',
        'Extra cash with trade-in of old phone',
        'Monthly telco bill',
        'Total cost of ownership over contractual period',
        'Ease of purchase at the shop / Online experience',
        'Location of M1 Shop',
        'Short waiting time',
        'Free delivery'
    ]

    rating_analysis = {}

    for factor in rating_factors:
        factor_col = get_column_index(headers, [factor])
        if factor_col:
            factor_ratings = []
            for row in data:
                if len(row) > factor_col and row[factor_col].strip():
                    rating = row[factor_col].strip()
                    if rating in ['Very important', 'Least important', '2', '3', '4']:
                        factor_ratings.append(rating)

            if factor_ratings:
                rating_counts = Counter(factor_ratings)
                total_ratings = len(factor_ratings)

                # Calculate importance score (Very important = 5, 4 = 4, 3 = 3, 2 = 2, Least important = 1)
                importance_score = 0
                for rating, count in rating_counts.items():
                    if rating == 'Very important':
                        importance_score += count * 5
                    elif rating == '4':
                        importance_score += count * 4
                    elif rating == '3':
                        importance_score += count * 3
                    elif rating == '2':
                        importance_score += count * 2
                    elif rating == 'Least important':
                        importance_score += count * 1

                avg_importance = importance_score / total_ratings if total_ratings > 0 else 0
                very_important_pct = (rating_counts.get('Very important', 0) / total_ratings) * 100 if total_ratings > 0 else 0

                rating_analysis[factor] = {
                    'ratings': dict(rating_counts),
                    'total_responses': total_ratings,
                    'avg_importance': avg_importance,
                    'very_important_pct': very_important_pct
                }

    # Sort by average importance score
    sorted_factors = sorted(rating_analysis.items(), key=lambda x: x[1]['avg_importance'], reverse=True)

    print("Rating Factors by Importance (Average Score):")
    for factor, analysis in sorted_factors:
        print(f"{factor:<45}: Avg={analysis['avg_importance']:.2f}, Very Important={analysis['very_important_pct']:.1f}%")

    # 7. Result 4: How did you learn about the M1 Anniversary Campaign
    print(f"\n{'='*60}")
    print("RESULT 4: HOW DID YOU LEARN ABOUT M1 ANNIVERSARY CAMPAIGN")
    print("="*60)

    # Define campaign awareness channels (columns 44-54)
    campaign_channels = [
        'TV ads',
        'Online ads',
        'Radio ads',
        'Social Media Post (Facebook, Instagram, Tik Tok)',
        'E-mailer',
        'M1 website',
        'SMS',
        'M1 App',
        'CEE trade show',
        'Word of mouth from friends, family',
        'Walked past the M1 Shop',
        'M1 Shop staff told me, prior to that I was not aware'
    ]

    campaign_awareness = {}

    for channel in campaign_channels:
        channel_col = get_column_index(headers, [channel])
        if channel_col:
            channel_responses = []
            for row in data:
                if len(row) > channel_col and row[channel_col].strip():
                    response = row[channel_col].strip()
                    if response.lower() == 'yes':
                        channel_responses.append(response)

            if channel_responses:
                campaign_awareness[channel] = len(channel_responses)

    # Sort by number of responses
    sorted_channels = sorted(campaign_awareness.items(), key=lambda x: x[1], reverse=True)
    total_aware_responses = sum(campaign_awareness.values())

    print("Campaign Awareness Channels:")
    for channel, count in sorted_channels:
        percentage = (count / total_aware_responses) * 100 if total_aware_responses > 0 else 0
        print(f"{channel:<50}: {count:>4} ({percentage:>5.1f}%)")

    # 8. Regression Analysis
    print(f"\n{'='*60}")
    print("REGRESSION-LIKE ANALYSIS: DEMOGRAPHIC INFLUENCES")
    print("="*60)

    regression_results = simple_regression_analysis(data, headers, demographics)

    # Show key demographic influences
    print("\nDemographic Influence on Promotion Motivation:")
    for demo_type, influences in regression_results.items():
        if 'motivation_promotion' in influences:
            print(f"\n{demo_type.replace('_', ' ').title()}:")
            sorted_influence = sorted(influences['motivation_promotion'].items(),
                                    key=lambda x: x[1], reverse=True)
            for demo_val, rate in sorted_influence[:5]:  # Top 5
                print(f"  {demo_val:<20}: {rate:>5.1f}% promotion motivated")

    print(f"\nDemographic Influence on Campaign Awareness:")
    for demo_type, influences in regression_results.items():
        if 'campaign_awareness' in influences:
            print(f"\n{demo_type.replace('_', ' ').title()}:")
            sorted_influence = sorted(influences['campaign_awareness'].items(),
                                    key=lambda x: x[1], reverse=True)
            for demo_val, rate in sorted_influence[:5]:  # Top 5
                print(f"  {demo_val:<20}: {rate:>5.1f}% campaign aware")

    # Save enhanced results
    enhanced_results = {
        'enhanced_motivation_analysis': {
            'main_categories': dict(motivation_counts),
            'others_subcategories': dict(others_subcategories),
            'word_cloud_data': dict(word_cloud_data.most_common(50))
        },
        'research_behavior': {k: dict(v) for k, v in research_data.items()},
        'demographics_summary': {k: dict(v) for k, v in demo_summary.items()},
        'demographic_slicing': slice_results,
        'cross_tabulations': cross_tabs,
        'rating_factors_analysis': rating_analysis,
        'campaign_awareness_channels': campaign_awareness,
        'regression_analysis': regression_results
    }

    with open('enhanced_analysis_results.json', 'w') as f:
        json.dump(enhanced_results, f, indent=2)

    print(f"\n{'='*60}")
    print("ENHANCED ANALYSIS COMPLETE")
    print("="*60)
    print("• Enhanced results saved to 'enhanced_analysis_results.json'")
    print("• Word cloud data generated for 'Others' category")
    print("• Demographic slicing and cross-tabulations completed")
    print("• Research behavior analysis included")
    print("• Result 3: Rating factors influencing phone purchase choice completed")
    print("• Result 4: How customers learned about M1 Anniversary campaign completed")
    print("• Regression analysis for demographic influences completed")

if __name__ == "__main__":
    main()
