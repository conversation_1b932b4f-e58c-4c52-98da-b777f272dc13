#!/usr/bin/env python3
"""
Simple Survey Analysis Script
Analyzes M1 Device Purchase Behavior Survey Data
"""

import csv
import re
from collections import Counter

def categorize_motivation(motivation_text):
    """Categorize customer motivation based on text analysis"""
    if not motivation_text or motivation_text.strip() == '':
        return 'Others'
    
    motivation_text = str(motivation_text).lower()
    
    # Define keywords for each category
    promotion_keywords = ['promotion', 'promo', 'discount', 'deal', 'offer', 'anniversary', 'trade in', 'trade-in', 
                         'rebate', 'free', 'attractive', 'good deal', 'great deal', 'birthday', 'campaign']
    
    upgrade_keywords = ['upgrade', 'new phone', 'latest', 'better', 'newer', 'technology', 'features', 
                       'camera', 'storage', 'performance', 'swift', 'switch brand', 'model']
    
    phone_issue_keywords = ['spoil', 'broken', 'damage', 'malfunction', 'battery', 'slow', 'lag', 'hang', 
                           'old phone', 'dying', 'issue', 'problem', 'not working', 'outdated']
    
    contract_keywords = ['contract', 'recontract', 're-contract', 'renewal', 'renew', 'end', 'ending', 
                        'expire', 'due', 'overdue']
    
    provider_keywords = ['switch provider', 'change provider', 'from singtel', 'from starhub', 
                        'cheaper than', 'better plan']
    
    # Check categories in order of priority
    if any(keyword in motivation_text for keyword in promotion_keywords):
        return 'Promotion'
    elif any(keyword in motivation_text for keyword in contract_keywords):
        return 'Contract ending'
    elif any(keyword in motivation_text for keyword in phone_issue_keywords):
        return 'Phone issue'
    elif any(keyword in motivation_text for keyword in upgrade_keywords):
        return 'Upgrade of phone'
    elif any(keyword in motivation_text for keyword in provider_keywords):
        return 'Switch Provider'
    else:
        return 'Others'

def main():
    print("=== M1 Device Purchase Behavior Survey Analysis ===")
    print()
    
    # Read CSV file with different encoding options
    try:
        # Try different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        data = None
        headers = None

        for encoding in encodings:
            try:
                with open('data-exploration-export_Device_wave2.csv', 'r', encoding=encoding) as file:
                    csv_reader = csv.reader(file)
                    headers = next(csv_reader)
                    data = list(csv_reader)
                print(f"Successfully read file with {encoding} encoding")
                break
            except UnicodeDecodeError:
                continue

        if data is None:
            raise Exception("Could not read file with any encoding")
        
        print(f"Successfully loaded data: {len(data)} rows, {len(headers)} columns")
        
        # Find motivation column (column M - "Motivation to Purchase")
        motivation_col_index = None
        for i, header in enumerate(headers):
            if 'Motivation to Purchase' in header:
                motivation_col_index = i
                print(f"Found motivation column at index {i}: '{header}'")
                break
        
        if motivation_col_index is None:
            print("ERROR: Could not find 'Motivation to Purchase' column")
            return
        
        # Analyze motivations
        print("\n" + "="*60)
        print("ANALYZING CUSTOMER MOTIVATIONS")
        print("="*60)
        
        categories = []
        sample_motivations = {}
        
        for row in data:
            if len(row) > motivation_col_index:
                motivation_text = row[motivation_col_index]
                category = categorize_motivation(motivation_text)
                categories.append(category)
                
                # Store sample motivations for each category
                if category not in sample_motivations:
                    sample_motivations[category] = []
                if len(sample_motivations[category]) < 3 and motivation_text.strip():
                    sample_motivations[category].append(motivation_text.strip())
        
        # Count categories
        motivation_counts = Counter(categories)
        total_responses = len(categories)
        
        print(f"\nMotivation Categories (Total Responses: {total_responses}):")
        print("-" * 60)
        
        for category, count in motivation_counts.most_common():
            percentage = (count / total_responses) * 100
            print(f"{category:<20}: {count:>4} ({percentage:>5.1f}%)")
        
        # Show sample motivations for each category
        print("\n" + "="*60)
        print("SAMPLE MOTIVATIONS BY CATEGORY")
        print("="*60)
        
        for category in motivation_counts.keys():
            print(f"\n{category.upper()}:")
            if category in sample_motivations:
                for i, sample in enumerate(sample_motivations[category][:3], 1):
                    print(f"  {i}. {sample}")
            else:
                print("  No samples available")
        
        # Create text-based visualization
        print("\n" + "="*60)
        print("MOTIVATION DISTRIBUTION CHART")
        print("="*60)
        
        max_count = max(motivation_counts.values())
        bar_width = 40
        
        for category, count in motivation_counts.most_common():
            percentage = (count / total_responses) * 100
            bar_length = int((count / max_count) * bar_width)
            bar = "█" * bar_length
            
            print(f"{category:<20} {bar:<40} {count:>4} ({percentage:>5.1f}%)")
        
        # Additional analyses for other results
        analyze_additional_metrics(data, headers)

        print(f"\nAnalysis complete! Total responses analyzed: {total_responses}")

    except FileNotFoundError:
        print("ERROR: Could not find 'data-exploration-export_Device_wave2.csv'")
        print("Please ensure the file is in the current directory.")
    except Exception as e:
        print(f"ERROR: {str(e)}")

def analyze_additional_metrics(data, headers):
    """Analyze additional metrics for Results 2, 3, and 4"""

    # Result 2: Campaign Awareness Analysis
    print("\n" + "="*60)
    print("RESULT 2: M1 ANNIVERSARY CAMPAIGN AWARENESS")
    print("="*60)

    campaign_col_index = None
    for i, header in enumerate(headers):
        if 'Were you aware of the M1 Anniversary Campaign?' in header:
            campaign_col_index = i
            break

    if campaign_col_index:
        campaign_responses = []
        for row in data:
            if len(row) > campaign_col_index and row[campaign_col_index].strip():
                campaign_responses.append(row[campaign_col_index].strip())

        campaign_counts = Counter(campaign_responses)
        total_campaign = len(campaign_responses)

        print(f"\nCampaign Awareness (Total Responses: {total_campaign}):")
        for response, count in campaign_counts.most_common():
            percentage = (count / total_campaign) * 100
            print(f"{response:<10}: {count:>4} ({percentage:>5.1f}%)")

    # Result 3: Device Type Analysis
    print("\n" + "="*60)
    print("RESULT 3: DEVICE TYPE PREFERENCES")
    print("="*60)

    device_col_index = None
    for i, header in enumerate(headers):
        if 'Device Type' in header:
            device_col_index = i
            break

    if device_col_index:
        device_responses = []
        for row in data:
            if len(row) > device_col_index and row[device_col_index].strip():
                device_responses.append(row[device_col_index].strip())

        device_counts = Counter(device_responses)
        total_devices = len(device_responses)

        print(f"\nDevice Types (Total Responses: {total_devices}):")
        for device, count in device_counts.most_common():
            percentage = (count / total_devices) * 100
            print(f"{device:<15}: {count:>4} ({percentage:>5.1f}%)")

    # Result 4: Age Demographics Analysis
    print("\n" + "="*60)
    print("RESULT 4: AGE DEMOGRAPHICS")
    print("="*60)

    age_col_index = None
    for i, header in enumerate(headers):
        if 'Age Band' in header:
            age_col_index = i
            break

    if age_col_index:
        age_responses = []
        for row in data:
            if len(row) > age_col_index and row[age_col_index].strip():
                age_responses.append(row[age_col_index].strip())

        age_counts = Counter(age_responses)
        total_ages = len(age_responses)

        print(f"\nAge Demographics (Total Responses: {total_ages}):")
        for age, count in age_counts.most_common():
            percentage = (count / total_ages) * 100
            print(f"{age:<15}: {count:>4} ({percentage:>5.1f}%)")

if __name__ == "__main__":
    main()
